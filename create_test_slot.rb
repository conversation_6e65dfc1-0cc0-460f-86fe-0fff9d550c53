#!/usr/bin/env ruby

# Script to debug timezone issues in slot creation
require_relative 'config/environment'

puts "Debugging Timezone Issues in Slot Creation"
puts "=" * 50

user = User.first
if user.nil?
  puts "No users found"
  exit 1
end

puts "User timezone: #{user.time_zone}"
puts "Rails timezone: #{Time.zone.name}"
puts "System timezone: #{Time.now.zone}"

# Test 1: Simulate what the frontend sends
puts "\n=== TEST 1: Simulating Frontend Data ==="
frontend_data = {
  start_time: "11:00",
  end_time: "11:30",
  day_of_week: "Saturday",
  is_recurring: false,
  specific_date: "2025-07-26",
  is_available: true
}

puts "Frontend sends: #{frontend_data.inspect}"

# Test 2: How <PERSON>s processes this data
puts "\n=== TEST 2: Rails Processing ==="
puts "Time.zone.parse('11:00'): #{Time.zone.parse('11:00').inspect}"
puts "Time.zone.parse('11:30'): #{Time.zone.parse('11:30').inspect}"

# Test 3: Create slot using the same process as the controller
puts "\n=== TEST 3: Controller-style Creation ==="
test_slot = user.faculty_time_slots.build(frontend_data)

# Manually set the time fields as Rails would
test_slot.start_time = Time.zone.parse(frontend_data[:start_time])
test_slot.end_time = Time.zone.parse(frontend_data[:end_time])

puts "Before save:"
puts "- start_time: #{test_slot.start_time.inspect}"
puts "- end_time: #{test_slot.end_time.inspect}"
puts "- start_time hour: #{test_slot.start_time.hour}"
puts "- start_time timezone: #{test_slot.start_time.zone}"

if test_slot.save
  puts "\n✅ Slot saved successfully!"
  puts "After save:"
  puts "- ID: #{test_slot.id}"
  puts "- start_time: #{test_slot.start_time.inspect}"
  puts "- end_time: #{test_slot.end_time.inspect}"
  puts "- start_time hour: #{test_slot.start_time.hour}"
  puts "- start_time timezone: #{test_slot.start_time.zone}"
else
  puts "\n❌ Slot failed to save:"
  test_slot.errors.full_messages.each { |msg| puts "  - #{msg}" }
end

# Test 4: Calendar data generation
puts "\n=== TEST 4: Calendar Data Generation ==="
controller = FacultyTimeSlotsController.new
controller.instance_variable_set(:@current_user, user)

start_date = Date.parse('2025-07-21')
end_date = Date.parse('2025-07-27')
date_range = start_date..end_date

formatted_slots = controller.send(:build_faculty_calendar_slots, user, date_range)

puts "Generated calendar slots:"
formatted_slots.each do |slot|
  datetime = Time.parse(slot[:datetime])
  puts "- #{datetime.strftime('%A, %B %d at %H:%M')} (#{slot[:datetime]})"
  puts "  Hour: #{datetime.hour}, Timezone: #{datetime.zone}"
end

# Test 5: Check what the calendar component would see
puts "\n=== TEST 5: Calendar Component Matching ==="
if formatted_slots.any?
  calendar_slot = formatted_slots.find { |s| s[:datetime].include?('11:00') }
  if calendar_slot
    calendar_datetime = Time.parse(calendar_slot[:datetime])
    calendar_time_string = "#{calendar_datetime.hour.to_s.rjust(2, '0')}:#{calendar_datetime.min.to_s.rjust(2, '0')}"
    puts "Calendar time string: #{calendar_time_string}"

    # Check if this matches what we expect
    if calendar_time_string == "11:00"
      puts "✅ Time string matches expected '11:00'"
    else
      puts "❌ Time string '#{calendar_time_string}' does NOT match expected '11:00'"
      puts "This is the timezone issue!"
    end
  else
    puts "❌ No 11:00 slot found in calendar data"
  end
end

puts "\nDebug completed!"
