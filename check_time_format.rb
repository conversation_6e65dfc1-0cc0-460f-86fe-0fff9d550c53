#!/usr/bin/env ruby

# Script to check time format consistency between database and calendar
require_relative 'config/environment'

puts "Checking Time Format Consistency"
puts "=" * 40

# Get the existing time slot
slot = FacultyTimeSlot.first
if slot.nil?
  puts "No time slots found in database"
  exit 1
end

puts "=== DATABASE TIME STORAGE ==="
puts "Raw start_time: #{slot.start_time.inspect}"
puts "Raw end_time: #{slot.end_time.inspect}"
puts "start_time class: #{slot.start_time.class}"
puts "start_time timezone: #{slot.start_time.zone}"

puts "\n=== TIME FORMATTING ==="
puts "start_time.strftime('%H:%M'): #{slot.start_time.strftime('%H:%M')}"
puts "start_time.strftime('%I:%M %p'): #{slot.start_time.strftime('%I:%M %p')}"
puts "start_time.hour: #{slot.start_time.hour}"
puts "start_time.min: #{slot.start_time.min}"

puts "\n=== CALENDAR DATA GENERATION ==="
user = User.first
controller = FacultyTimeSlotsController.new
controller.instance_variable_set(:@current_user, user)

start_date = Date.parse('2025-07-21')
end_date = Date.parse('2025-07-27')
date_range = start_date..end_date

formatted_slots = controller.send(:build_faculty_calendar_slots, user, date_range)
if formatted_slots.empty?
  puts "No calendar slots generated!"
  exit 1
end

calendar_slot = formatted_slots.first

puts "Calendar slot datetime: #{calendar_slot[:datetime]}"
puts "Calendar slot formatted_time: #{calendar_slot[:formatted_time]}"

# Parse the calendar datetime and compare
calendar_datetime = Time.parse(calendar_slot[:datetime])
puts "Parsed calendar datetime: #{calendar_datetime.inspect}"
puts "Calendar datetime hour: #{calendar_datetime.hour}"
puts "Calendar datetime min: #{calendar_datetime.min}"
puts "Calendar datetime timezone: #{calendar_datetime.zone}"

puts "\n=== COMPARISON ==="
puts "Database slot hour: #{slot.start_time.hour}"
puts "Calendar slot hour: #{calendar_datetime.hour}"
puts "Hours match: #{slot.start_time.hour == calendar_datetime.hour}"

puts "Database slot minute: #{slot.start_time.min}"
puts "Calendar slot minute: #{calendar_datetime.min}"
puts "Minutes match: #{slot.start_time.min == calendar_datetime.min}"

puts "\n=== CALENDAR COMPONENT TIME MATCHING ==="
# Simulate what the calendar component does to match times
calendar_time_string = "#{calendar_datetime.hour.to_s.rjust(2, '0')}:#{calendar_datetime.min.to_s.rjust(2, '0')}"
puts "Calendar time string for matching: #{calendar_time_string}"

# Check if this time string would be in the calendar's time slot list
puts "\nChecking if time is in calendar's generated time slots..."
old_time_slots = []
for hour in 8...18  # Old calendar range
  for minute in [0, 30]
    time_string = "#{hour.to_s.rjust(2, '0')}:#{minute.to_s.rjust(2, '0')}"
    old_time_slots << time_string
  end
end

puts "OLD Calendar time slots (8 AM - 6 PM):"
puts old_time_slots.join(', ')

if old_time_slots.include?(calendar_time_string)
  puts "\n✅ Time slot #{calendar_time_string} IS in OLD calendar range"
else
  puts "\n❌ Time slot #{calendar_time_string} is NOT in OLD calendar range"
  puts "This WAS why the slot was not visible in the calendar!"
end

puts "\n=== NEW EXTENDED RANGE CHECK ==="
# Check with NEW extended range (6 AM - 11 PM) - this is what we just implemented
new_time_slots = []
for hour in 6..23
  for minute in [0, 30]
    next if hour == 23 && minute == 30  # Don't include 23:30
    time_string = "#{hour.to_s.rjust(2, '0')}:#{minute.to_s.rjust(2, '0')}"
    new_time_slots << time_string
  end
end

puts "NEW Calendar time slots (6 AM - 11 PM):"
puts new_time_slots.join(', ')

if new_time_slots.include?(calendar_time_string)
  puts "\n✅ Time slot #{calendar_time_string} IS NOW visible with NEW extended range (6 AM - 11 PM)"
  puts "🎉 THE FIX SHOULD WORK!"
else
  puts "\n❌ Time slot #{calendar_time_string} would still NOT be visible with extended range"
  puts "😞 The fix needs more work"
end

puts "\nNew range includes #{new_time_slots.length} time slots vs #{old_time_slots.length} in old range"
